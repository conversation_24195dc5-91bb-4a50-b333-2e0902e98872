from maix import image, display, app, time, camera
import cv2
import numpy as np

disp = display.Display()    
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

# 定义矩形过滤参数
min_area = 2000  # 最小矩形面积
max_area = 40000  # 最大矩形面积
min_aspect_ratio = 0.2  # 最小长宽比
max_aspect_ratio = 5  # 最大长宽比

while not app.need_exit():
    img = cam.read()
    # convert maix.image.Image object to numpy.ndarray object
    cv_img = image.image2cv(img, ensure_bgr=False, copy=False)

    # 转换为HSV颜色空间
    hsv = cv2.cvtColor(cv_img, cv2.COLOR_BGR2HSV)

    # 定义白色的HSV阈值范围
    # 注意：这个范围可能需要根据实际光照条件进行调整
    lower_white = np.array([110, 20, 162])
    upper_white = np.array([180, 255, 255])

    # 根据阈值创建掩码
    mask = cv2.inRange(hsv, lower_white, upper_white)

    # 对掩码进行一些形态学操作以去除噪声
    kernel = np.ones((5, 5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

    # 在掩码上查找轮廓
    contours, _ = cv2.findContours(mask.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 矩形检测
    rectangles = []
    for contour in contours:
        perimeter = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.04 * perimeter, True)
        if len(approx) == 4:
            # 计算矩形的面积
            area = cv2.contourArea(approx)
            # 计算矩形的边界框
            x, y, w, h = cv2.boundingRect(approx)
            # 计算长宽比
            aspect_ratio = float(w) / h if h != 0 else 0
            # 过滤不符合条件的矩形
            if min_area < area < max_area and min_aspect_ratio < aspect_ratio < max_aspect_ratio:
                rectangles.append(approx)

    # 绘制所有检测到的矩形
    for rect in rectangles:
        color = (0, 255, 0)  # 绿色
        label = "Rectangle"
        cv2.drawContours(cv_img, [rect], -1, color, 2)
        cv2.putText(cv_img, label, (rect[0][0][0], rect[0][0][1] - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

    # show by maix.display
    img_show = image.cv2image(cv_img, bgr=True, copy=False)
    disp.show(img_show)