from maix import image, display, app, time, camera, uart
import cv2
import numpy as np
import math


# ======== 正弦波配置类 ========
class SineWaveConfig:
    """正弦波轨迹配置类"""
    def __init__(self):
        self.amplitude = 50          # 振幅（像素）
        self.frequency = 2.0         # 频率（周期数/屏幕宽度）
        self.phase = 0.0            # 相位偏移（弧度）
        self.center_x = 160         # 中心X坐标（屏幕中央）
        self.center_y = 120         # 中心Y坐标（屏幕中央）
        self.speed = 1.0            # 移动速度倍数
        self.points_count = 100     # 轨迹点数量


class TrackingConfig:
    """激光跟踪配置类"""
    def __init__(self):
        self.target_threshold = 20  # 激光到达目标点的距离阈值（像素）


# ======== 正弦波生成和跟踪类 ========
class SineWaveGenerator:
    """正弦波轨迹生成器类"""
    def __init__(self, config=None):
        self.config = config or SineWaveConfig()
        self.start_time = None
        self.screen_width = 320     # 屏幕宽度
        self.screen_height = 240    # 屏幕高度

    def generate_path(self):
        """生成完整正弦波路径点列表"""
        path_points = []
        
        # 计算X轴范围（从左到右跨越整个屏幕）
        x_start = 0
        x_end = self.screen_width
        x_step = (x_end - x_start) / self.config.points_count
        
        for i in range(self.config.points_count):
            # 计算当前X坐标
            x = x_start + i * x_step
            
            # 计算正弦波Y坐标
            # 将X坐标映射到正弦波的角度范围
            angle = (x / self.screen_width) * self.config.frequency * 2 * math.pi + self.config.phase
            y_offset = self.config.amplitude * math.sin(angle)
            y = self.config.center_y + y_offset
            
            # 确保坐标在屏幕范围内
            x = max(0, min(self.screen_width - 1, int(x)))
            y = max(0, min(self.screen_height - 1, int(y)))
            
            path_points.append((x, y))
        
        return path_points

    def get_current_target(self, current_time):
        """根据当前时间获取目标点坐标"""
        if self.start_time is None:
            self.start_time = current_time
        
        # 计算经过的时间（毫秒）
        elapsed_time = current_time - self.start_time
        
        # 计算当前X位置（基于时间和速度）
        # 每10秒完成一个完整周期
        cycle_duration = 10000  # 10秒 = 10000毫秒
        progress = (elapsed_time * self.config.speed) % cycle_duration / cycle_duration
        
        # 计算当前X坐标
        x = progress * self.screen_width
        
        # 计算正弦波Y坐标
        angle = progress * self.config.frequency * 2 * math.pi + self.config.phase
        y_offset = self.config.amplitude * math.sin(angle)
        y = self.config.center_y + y_offset
        
        # 确保坐标在屏幕范围内
        x = max(0, min(self.screen_width - 1, int(x)))
        y = max(0, min(self.screen_height - 1, int(y)))
        
        return (x, y)

    def _calculate_sine_point(self, x_position):
        """计算指定X位置的正弦波Y坐标"""
        # 将X坐标映射到正弦波的角度范围
        angle = (x_position / self.screen_width) * self.config.frequency * 2 * math.pi + self.config.phase
        y_offset = self.config.amplitude * math.sin(angle)
        y = self.config.center_y + y_offset
        
        # 确保Y坐标在屏幕范围内
        y = max(0, min(self.screen_height - 1, int(y)))
        
        return y


class LaserTracker:
    """激光跟踪器类"""
    def __init__(self, config=None):
        self.config = config or TrackingConfig()

    def update(self, laser_coords, target_point):
        """更新激光目标点，检查是否到达当前目标"""
        if target_point is None or laser_coords is None:
            return False
        
        # 计算激光点到目标点的距离
        distance = np.linalg.norm(np.array(laser_coords) - np.array(target_point))
        
        # 如果激光到达目标点范围内，返回True
        return distance <= self.config.target_threshold


# ======== 全局配置实例 ========
SINE_CONFIG = SineWaveConfig()      # 正弦波配置
TRACKING_CONFIG = TrackingConfig()  # 激光跟踪配置

# 正弦波轨迹跟踪参数
sine_wave_generator = SineWaveGenerator(SINE_CONFIG)
laser_tracker = LaserTracker(TRACKING_CONFIG)
path_points = []                    # 完整正弦波路径点（用于可视化）
current_target_point = None         # 当前目标点
sine_wave_active = False           # 正弦波激活状态


def generate_sine_wave_path():
    """生成正弦波路径点（兼容性wrapper函数）"""
    global path_points
    path_points = sine_wave_generator.generate_path()
    return path_points


def update_sine_target(current_time):
    """更新正弦波目标点（基于时间）"""
    global current_target_point
    current_target_point = sine_wave_generator.get_current_target(current_time)
    return current_target_point


def check_laser_target_reached(laser_coords):
    """检查激光是否到达目标点"""
    if current_target_point is None:
        return False
    return laser_tracker.update(laser_coords, current_target_point)
