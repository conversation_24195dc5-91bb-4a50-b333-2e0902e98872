from maix import image, display, app, time, camera, uart
import cv2
import numpy as np
import math


# ======== 正弦波配置类 ========
class SineWaveConfig:
    """正弦波轨迹配置类"""
    def __init__(self):
        self.amplitude = 50          # 振幅（像素）
        self.frequency = 2.0         # 频率（周期数/屏幕宽度）
        self.phase = 0.0            # 相位偏移（弧度）
        self.center_x = 160         # 中心X坐标（屏幕中央）
        self.center_y = 120         # 中心Y坐标（屏幕中央）
        self.speed = 0.4            # 移动速度倍数
        self.points_count = 100     # 轨迹点数量


class TrackingConfig:
    """激光跟踪配置类"""
    def __init__(self):
        self.target_threshold = 20  # 激光到达目标点的距离阈值（像素）


# ======== 正弦波生成和跟踪类 ========
class SineWaveGenerator:
    """正弦波轨迹生成器类"""
    def __init__(self, config=None):
        self.config = config or SineWaveConfig()
        self.start_time = None
        self.screen_width = 320     # 屏幕宽度
        self.screen_height = 240    # 屏幕高度

    def generate_path(self):
        """生成完整正弦波路径点列表"""
        path_points = []
        
        # 计算X轴范围（从左到右跨越整个屏幕）
        x_start = 0
        x_end = self.screen_width
        x_step = (x_end - x_start) / self.config.points_count
        
        for i in range(self.config.points_count):
            # 计算当前X坐标
            x = x_start + i * x_step
            
            # 计算正弦波Y坐标
            # 将X坐标映射到正弦波的角度范围
            angle = (x / self.screen_width) * self.config.frequency * 2 * math.pi + self.config.phase
            y_offset = self.config.amplitude * math.sin(angle)
            y = self.config.center_y + y_offset
            
            # 确保坐标在屏幕范围内
            x = max(0, min(self.screen_width - 1, int(x)))
            y = max(0, min(self.screen_height - 1, int(y)))
            
            path_points.append((x, y))
        
        return path_points

    def get_current_target(self, current_time):
        """根据当前时间获取目标点坐标"""
        if self.start_time is None:
            self.start_time = current_time
        
        # 计算经过的时间（毫秒）
        elapsed_time = current_time - self.start_time
        
        # 计算当前X位置（基于时间和速度）
        # 每10秒完成一个完整周期
        cycle_duration = 10000  # 10秒 = 10000毫秒
        progress = (elapsed_time * self.config.speed) % cycle_duration / cycle_duration
        
        # 计算当前X坐标
        x = progress * self.screen_width
        
        # 计算正弦波Y坐标
        angle = progress * self.config.frequency * 2 * math.pi + self.config.phase
        y_offset = self.config.amplitude * math.sin(angle)
        y = self.config.center_y + y_offset
        
        # 确保坐标在屏幕范围内
        x = max(0, min(self.screen_width - 1, int(x)))
        y = max(0, min(self.screen_height - 1, int(y)))
        
        return (x, y)


class LaserTracker:
    """激光跟踪器类"""
    def __init__(self, config=None):
        self.config = config or TrackingConfig()

    def check_target_reached(self, laser_coords, target_point):
        """检查激光是否到达单个目标点"""
        if target_point is None or laser_coords is None:
            return False
        
        # 计算激光点到目标点的距离
        distance = np.linalg.norm(np.array(laser_coords) - np.array(target_point))
        
        # 如果激光到达目标点范围内，返回True
        return distance <= self.config.target_threshold


# ======== 全局配置实例 ========
SINE_CONFIG = SineWaveConfig()      # 正弦波配置
TRACKING_CONFIG = TrackingConfig()  # 激光跟踪配置

# 正弦波轨迹跟踪参数
sine_wave_generator = SineWaveGenerator(SINE_CONFIG)
laser_tracker = LaserTracker(TRACKING_CONFIG)
path_points = []                    # 完整正弦波路径点（用于可视化）
current_target_point = None         # 当前目标点
sine_wave_active = False           # 正弦波激活状态


def generate_sine_wave_path():
    """生成正弦波路径点（兼容性wrapper函数）"""
    global path_points
    path_points = sine_wave_generator.generate_path()
    return path_points


def update_sine_target(current_time):
    """更新正弦波目标点（基于时间）"""
    global current_target_point
    current_target_point = sine_wave_generator.get_current_target(current_time)
    return current_target_point


def check_laser_target_reached(laser_coords):
    """检查激光是否到达目标点"""
    if current_target_point is None:
        return False
    return laser_tracker.check_target_reached(laser_coords, current_target_point)


# ======== 曝光控制参数（可调节） ========
EXPOSURE_MODE = 'auto'  # 曝光模式：'auto' 或 'manual' - 激光检测建议用auto
MANUAL_EXPOSURE_VALUE = 1000  # 手动曝光值 (微秒)，范围通常为 1-1000


# ======== 激光检测功能 ========
def detect_red_laser(img):
    """检测红色激光点，只返回坐标不绘制"""
    # 转换颜色空间为 HSV
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    
    # 定义红色激光点颜色范围
    lower_red1 = np.array([160, 82, 40])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 100, 50])
    upper_red2 = np.array([180, 255, 255])
    
    # 创建红色激光的二值化图像
    mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
    mask_red = cv2.bitwise_or(mask_red1, mask_red2)
    
    # 闭运算
    kernel = np.ones((5, 5), np.uint8)
    mask_red = cv2.morphologyEx(mask_red, cv2.MORPH_CLOSE, kernel)
    
    # 寻找红色激光外轮廓
    contours_red, _ = cv2.findContours(mask_red, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    red_laser_coords = None
    
    for contour in contours_red:
        # 找到最小矩形框
        rect = cv2.minAreaRect(contour)
        # 直接从元组中提取中心坐标
        laser_coords = tuple(map(int, rect[0]))
        r_sum, g_sum = get_pixel_sum(img, laser_coords)
        if r_sum > g_sum:
            red_laser_coords = laser_coords
            break  # 只检测第一个红色激光点
    
    return red_laser_coords


def get_pixel_sum(image, coords):
    """获取指定坐标周围区域的R和G通道像素值总和"""
    # 获取图像宽度和高度
    height, width = image.shape[:2]
    radius = 3
    # 确定方圆的左上角和右下角坐标
    x, y = coords
    x_start = max(0, x - radius)
    y_start = max(0, y - radius)
    x_end = min(width - 1, x + radius)
    y_end = min(height - 1, y + radius)
    
    # 提取方圆区域
    roi = image[y_start:y_end, x_start:x_end]
    
    # 计算 R 和 G 通道总值
    r_channel = roi[:, :, 2]  # 红色通道
    g_channel = roi[:, :, 1]  # 绿色通道
    r_sum = int(r_channel.sum())
    g_sum = int(g_channel.sum())
    
    return r_sum, g_sum


# ======== 串口通信功能 ========
# 初始化串口
serial = uart.UART("/dev/ttyS0", 115200)

# 串口通信控制标志
data_send_enabled = True  # 数据发送使能标志


def check_serial_command():
    """检查串口接收到的启动指令、重置指令和参数调整指令"""
    global data_send_enabled, sine_wave_active, current_target_point
    global sine_wave_generator, path_points

    # 非阻塞读取串口数据
    received_data = serial.read(len=-1, timeout=0)  # 读取所有可用数据，不等待
    if received_data:
        # 检查是否收到启动指令 "FF 00"
        if b'\xFF\x00' in received_data:
            data_send_enabled = True
        
        # 检查是否收到重置指令 "FF FF" - 重置所有参数到默认值
        if b'\xFF\xFF' in received_data:
            sine_wave_active = False
            current_target_point = None
            sine_wave_generator.start_time = None  # 重置开始时间
            # 重置参数到默认值
            sine_wave_generator.config.amplitude = 50
            sine_wave_generator.config.frequency = 2.0
            sine_wave_generator.config.speed = 1.0
            sine_wave_generator.config.phase = 0.0
            # 重新生成路径点
            path_points = sine_wave_generator.generate_path()
            print("Sine wave parameters reset to default values")
        
        # 检查参数调整指令
        if len(received_data) >= 3:
            for i in range(len(received_data) - 2):
                if received_data[i] == 0xFF:
                    cmd = received_data[i + 1]
                    param = received_data[i + 2]
                    
                    if cmd == 0x01:  # FF 01 + 参数: 调整振幅
                        new_amplitude = max(10, min(100, param))  # 限制范围 10-100
                        sine_wave_generator.config.amplitude = new_amplitude
                        path_points = sine_wave_generator.generate_path()
                        print(f"Amplitude adjusted to: {new_amplitude}")
                    
                    elif cmd == 0x02:  # FF 02 + 参数: 调整频率
                        new_frequency = max(0.5, min(5.0, param / 10.0))  # 限制范围 0.5-5.0
                        sine_wave_generator.config.frequency = new_frequency
                        path_points = sine_wave_generator.generate_path()
                        print(f"Frequency adjusted to: {new_frequency:.1f}")
                    
                    elif cmd == 0x03:  # FF 03 + 参数: 调整速度
                        new_speed = max(0.1, min(3.0, param / 10.0))  # 限制范围 0.1-3.0
                        sine_wave_generator.config.speed = new_speed
                        print(f"Speed adjusted to: {new_speed:.1f}")

    return data_send_enabled


def send_position_data(laser_pos, target_pos):
    """发送位置数据包：78 target_x_high target_x_low target_y_high target_y_low laser_x_high laser_x_low laser_y_high laser_y_low fc"""
    # 检查是否允许发送数据
    if not data_send_enabled:
        return

    # 检查是否检测到激光点
    if laser_pos is None:
        return

    # 获取坐标值
    laser_x = laser_pos[0]
    laser_y = laser_pos[1]
    target_x = target_pos[0] if target_pos else 0
    target_y = target_pos[1] if target_pos else 0

    # 构建数据包 (10字节) - 目标点在前，激光点在后
    data_list = [
        0x78,                   # 包头
        (target_x >> 8) & 0xFF, # 目标x高八位
        target_x & 0xFF,        # 目标x低八位
        (target_y >> 8) & 0xFF, # 目标y高八位
        target_y & 0xFF,        # 目标y低八位
        (laser_x >> 8) & 0xFF,  # 激光x高八位
        laser_x & 0xFF,         # 激光x低八位
        (laser_y >> 8) & 0xFF,  # 激光y高八位
        laser_y & 0xFF,         # 激光y低八位
        0xFC                    # 包尾
    ]

    # 转换为bytes类型
    data_packet = bytes(data_list)

    serial.write(data_packet)
    print(f"目标({target_x},{target_y}) 实际({laser_x},{laser_y})")


# ======== 相机和显示系统初始化 ========
# 初始化相机和显示
disp = display.Display()
cam = camera.Camera(320, 240, image.Format.FMT_BGR888)

# 设置曝光参数
if EXPOSURE_MODE == 'manual':
    cam.exposure(MANUAL_EXPOSURE_VALUE)
    cam.gain(10000)  # 设置增益值，需在手动曝光模式下使用
else:
    cam.exp_mode(0)


# ======== 显示功能 ========
def draw_status_info(img, sine_wave_active, red_laser_coords, data_send_enabled, data_sending):
    """绘制简化的状态信息"""
    # 显示激光检测状态
    laser_status = "Laser: ON" if red_laser_coords is not None else "Laser: OFF"
    laser_color = (0, 255, 0) if red_laser_coords is not None else (0, 0, 255)
    cv2.putText(img, laser_status, (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, laser_color, 1)

    # 官方帧率显示
    fps = time.fps()
    fps_text = f"FPS: {fps:.1f}"
    cv2.putText(img, fps_text, (10, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)


def draw_sine_wave_visualization(img, path_points, current_target_point):
    """绘制简化的正弦波轨迹"""
    if not path_points:
        return

    # 绘制正弦波轨迹（蓝色细线）
    for i in range(len(path_points) - 1):
        cv2.line(img, path_points[i], path_points[i + 1], (255, 0, 0), 1)

    # 显示当前目标点（黄色圆点）
    if current_target_point is not None:
        cv2.circle(img, current_target_point, 5, (0, 255, 255), -1)


def draw_laser_point(img, red_laser_coords):
    """绘制激光点"""
    if red_laser_coords is not None:
        cv2.circle(img, red_laser_coords, 3, (0, 0, 255), -1)


# ======== 主程序循环 ========
def main():
    """主程序循环"""
    global sine_wave_active, current_target_point, path_points

    # 生成正弦波路径点用于可视化
    path_points = generate_sine_wave_path()

    while not app.need_exit():
        # 检查串口启动指令
        check_serial_command()

        # 读取相机图像
        img = cam.read()
        if img is None:
            time.sleep(0.1)
            continue

        # 转换为OpenCV格式
        img = image.image2cv(img, ensure_bgr=False, copy=False)

        # 检测红色激光点
        red_laser_coords = detect_red_laser(img)

        # 更新正弦波目标点（基于时间）
        current_time = time.ticks_ms()
        current_target_point = update_sine_target(current_time)

        # 激活正弦波轨迹
        if not sine_wave_active and current_target_point is not None:
            sine_wave_active = True
            print("正弦波轨迹已激活")

        # 绘制正弦波轨迹可视化
        draw_sine_wave_visualization(img, path_points, current_target_point)

        # 绘制激光点
        draw_laser_point(img, red_laser_coords)

        # 发送串口数据包（只有检测到激光点时才发送）
        data_sending = False
        if sine_wave_active and current_target_point is not None and red_laser_coords is not None:
            send_position_data(red_laser_coords, current_target_point)
            data_sending = True

        # 绘制状态信息
        draw_status_info(img, sine_wave_active, red_laser_coords, data_send_enabled, data_sending)

        # 显示结果
        disp.show(image.cv2image(img, bgr=True, copy=False))


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序异常: {e}")
    finally:
        print("正弦波激光跟踪系统已退出")
