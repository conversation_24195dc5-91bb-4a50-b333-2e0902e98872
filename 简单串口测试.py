from maix import uart, app, time

def main():
    """简单串口测试程序 - 专门测试三角形识别通信"""
    print("=== 简单串口测试程序 ===")
    print("用途：测试与三角形识别程序的串口通信")
    print("功能：发送启动指令，接收位置数据包")
    print("按 Ctrl+C 退出")
    print("=" * 35)
    
    # 初始化串口
    serial = uart.UART("/dev/ttyS0", 115200)
    print("串口初始化成功")
    
    # 等待用户准备
    print("\n请确保三角形识别程序已启动...")
    time.sleep(2)
    
    # 发送启动指令
    print("发送启动指令 'FF 00'...")
    serial.write(b'\xFF\x00')
    print("✅ 启动指令已发送 (FF 00)")
    
    print("\n开始监听位置数据包...")
    print("数据包格式: 78 target_x_h target_x_l target_y_h target_y_l laser_x_h laser_x_l laser_y_h laser_y_l FC")
    print("-" * 60)
    
    packet_count = 0
    
    while not app.need_exit():
        # 接收数据
        data = serial.read(len=-1, timeout=100)  # 100ms超时
        
        if data:
            packet_count += 1
            print(f"\n📦 数据包 #{packet_count}")
            print(f"原始数据: {data.hex().upper()}")
            
            # 解析数据包
            if len(data) >= 10 and data[0] == 0x78 and data[9] == 0xFC:
                # 解析位置数据
                target_x = (data[1] << 8) | data[2]
                target_y = (data[3] << 8) | data[4]
                laser_x = (data[5] << 8) | data[6]
                laser_y = (data[7] << 8) | data[8]
                
                print(f"🎯 目标点: ({target_x}, {target_y})")
                print(f"🔴 激光点: ({laser_x}, {laser_y})")
                print(f"📏 距离: {((target_x-laser_x)**2 + (target_y-laser_y)**2)**0.5:.1f} 像素")
            else:
                print("⚠️  数据包格式不正确")
        
        time.sleep_ms(50)  # 50ms间隔

if __name__ == "__main__":
    main()
