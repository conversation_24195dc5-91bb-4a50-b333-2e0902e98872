from maix import image, display, app, time, camera
import cv2
import numpy as np

disp = display.Display()
cam = camera.Camera()

MIN_AREA, MAX_AREA = 2000, 40000
MIN_RATIO, MAX_RATIO = 0.2, 5
CORNER_THRESH = 8

def detect_rectangles(contours):
    rectangles = []
    for contour in contours:
        perimeter = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.04 * perimeter, True)
        if len(approx) == 4:
            area = cv2.contourArea(approx)
            x, y, w, h = cv2.boundingRect(approx)
            ratio = float(w) / h if h != 0 else 0
            if MIN_AREA < area < MAX_AREA and MIN_RATIO < ratio < MAX_RATIO:
                rectangles.append(approx)
    return rectangles

def merge_rectangles(rectangles):
    merged = []
    for rect in rectangles:
        found = False
        for i, merged_rect in enumerate(merged):
            matches = sum(1 for c1 in rect for c2 in merged_rect 
                         if np.linalg.norm(c1[0] - c2[0]) < CORNER_THRESH)
            if matches >= 3:
                if cv2.contourArea(rect) > cv2.contourArea(merged_rect):
                    merged[i] = rect
                found = True
                break
        if not found:
            merged.append(rect)
    return sorted(merged, key=cv2.contourArea, reverse=True)[:2]

def draw_rectangles(img, rectangles):
    if len(rectangles) == 2:
        colors = [(0, 0, 255), (0, 255, 0)]
        labels = ["Outer Frame", "Inner Frame"]
        for rect, color, label in zip(rectangles, colors, labels):
            cv2.drawContours(img, [rect], -1, color, 2)
            cv2.putText(img, label, tuple(rect[0][0] - [0, 10]), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    elif len(rectangles) == 1:
        rect = rectangles[0]
        area = cv2.contourArea(rect)
        color = (0, 0, 255) if area > (MAX_AREA + MIN_AREA) / 2 else (0, 255, 0)
        label = "Outer Frame" if area > (MAX_AREA + MIN_AREA) / 2 else "Inner Frame"
        cv2.drawContours(img, [rect], -1, color, 2)
        cv2.putText(img, label, tuple(rect[0][0] - [0, 10]), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

while not app.need_exit():
    img = cam.read()
    if img is None:
        time.sleep(0.1)
        continue
    
    cv_img = img.numpy()
    gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    edges = cv2.Canny(blurred, 50, 150)
    dilated = cv2.dilate(edges, np.ones((3, 3), np.uint8), iterations=1)
    
    contours, _ = cv2.findContours(dilated, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    rectangles = detect_rectangles(contours)
    merged_rects = merge_rectangles(rectangles)
    draw_rectangles(cv_img, merged_rects)
    
    disp.show(image.from_numpy(cv_img))
    