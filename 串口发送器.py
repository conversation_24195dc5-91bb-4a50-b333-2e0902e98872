from maix import uart, app, time

def main():
    """串口发送器 - 向三角形识别程序发送控制指令"""
    print("=== 串口发送器 ===")
    print("用途：向三角形识别程序发送控制指令")
    print("指令列表：")
    print("  'FF 00' - 启动数据发送")
    print("  'FF FF' - 停止数据发送（如果支持）")
    print("  '?'     - 状态查询（如果支持）")
    print("=" * 30)
    
    # 初始化串口
    serial = uart.UART("/dev/ttyS0", 115200)
    print("串口初始化成功")
    
    print("\n自动发送测试序列...")
    
    # 测试序列
    test_commands = [
        (b'\xFF\x00', "启动指令 FF 00"),
        (b'\xFF\xFF', "停止指令 FF FF"),
        (b'\xFF\x00', "重新启动 FF 00"),
        ("?", "状态查询"),
        ("TEST", "测试字符串"),
        (b'\xFF\x00', "最终启动 FF 00")
    ]
    
    for i, (cmd, desc) in enumerate(test_commands):
        print(f"\n{i+1}. 发送 - {desc}")

        if isinstance(cmd, bytes):
            serial.write(cmd)
            print(f"   二进制数据: {cmd.hex().upper()}")
        else:
            serial.write_str(cmd)
            print(f"   文本数据: '{cmd}'")

        # 检查是否有回应
        time.sleep_ms(500)
        response = serial.read(len=-1, timeout=0)
        if response:
            print(f"   收到回应: {response}")
        else:
            print("   无回应")

        time.sleep(2)  # 等待2秒
    
    print("\n测试序列完成")
    print("程序将持续发送启动指令...")
    
    # 持续发送启动指令
    count = 0
    while not app.need_exit():
        count += 1
        print(f"发送启动指令 FF 00 #{count}")
        serial.write(b'\xFF\x00')
        time.sleep(5)  # 每5秒发送一次

if __name__ == "__main__":
    main()
