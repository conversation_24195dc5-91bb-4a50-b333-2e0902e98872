#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
正弦波配置和生成器类测试脚本
"""

import sys
import time
import math

# 导入正弦波模块
try:
    from 正弦波激光跟踪 import SineWaveConfig, SineWaveGenerator, generate_sine_wave_path, update_sine_target
    print("✅ 成功导入正弦波模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_sine_wave_config():
    """测试SineWaveConfig类"""
    print("\n=== 测试SineWaveConfig类 ===")
    config = SineWaveConfig()
    
    # 检查默认参数
    assert config.amplitude == 50, f"振幅应为50，实际为{config.amplitude}"
    assert config.frequency == 2.0, f"频率应为2.0，实际为{config.frequency}"
    assert config.phase == 0.0, f"相位应为0.0，实际为{config.phase}"
    assert config.center_x == 160, f"中心X应为160，实际为{config.center_x}"
    assert config.center_y == 120, f"中心Y应为120，实际为{config.center_y}"
    assert config.speed == 1.0, f"速度应为1.0，实际为{config.speed}"
    assert config.points_count == 100, f"点数量应为100，实际为{config.points_count}"
    
    print(f"✅ 振幅: {config.amplitude}")
    print(f"✅ 频率: {config.frequency}")
    print(f"✅ 相位: {config.phase}")
    print(f"✅ 中心位置: ({config.center_x}, {config.center_y})")
    print(f"✅ 速度: {config.speed}")
    print(f"✅ 点数量: {config.points_count}")

def test_sine_wave_generator():
    """测试SineWaveGenerator类"""
    print("\n=== 测试SineWaveGenerator类 ===")
    config = SineWaveConfig()
    generator = SineWaveGenerator(config)
    
    # 测试路径生成
    path_points = generator.generate_path()
    assert len(path_points) == config.points_count, f"路径点数量应为{config.points_count}，实际为{len(path_points)}"
    
    print(f"✅ 生成路径点数量: {len(path_points)}")
    print(f"✅ 前5个点: {path_points[:5]}")
    print(f"✅ 后5个点: {path_points[-5:]}")
    
    # 检查坐标范围
    x_coords = [p[0] for p in path_points]
    y_coords = [p[1] for p in path_points]
    
    assert all(0 <= x < 320 for x in x_coords), "X坐标超出屏幕范围"
    assert all(0 <= y < 240 for y in y_coords), "Y坐标超出屏幕范围"
    
    print(f"✅ X坐标范围: {min(x_coords)} - {max(x_coords)}")
    print(f"✅ Y坐标范围: {min(y_coords)} - {max(y_coords)}")
    
    # 测试时间驱动的目标点
    current_time = int(time.time() * 1000)  # 模拟time.ticks_ms()
    target1 = generator.get_current_target(current_time)
    target2 = generator.get_current_target(current_time + 1000)  # 1秒后
    target3 = generator.get_current_target(current_time + 2000)  # 2秒后
    
    # 检查目标点坐标范围
    for target in [target1, target2, target3]:
        assert 0 <= target[0] < 320, f"目标点X坐标超出范围: {target[0]}"
        assert 0 <= target[1] < 240, f"目标点Y坐标超出范围: {target[1]}"
    
    print(f"✅ 时间0: {target1}")
    print(f"✅ 时间1000ms: {target2}")
    print(f"✅ 时间2000ms: {target3}")

def test_wrapper_functions():
    """测试wrapper函数"""
    print("\n=== 测试wrapper函数 ===")
    
    # 测试路径生成wrapper
    wrapper_path = generate_sine_wave_path()
    assert len(wrapper_path) > 0, "wrapper路径生成失败"
    print(f"✅ wrapper生成点数量: {len(wrapper_path)}")
    
    # 测试目标点更新wrapper
    current_time = int(time.time() * 1000)
    wrapper_target = update_sine_target(current_time)
    assert wrapper_target is not None, "wrapper目标点生成失败"
    assert 0 <= wrapper_target[0] < 320, f"wrapper目标点X坐标超出范围: {wrapper_target[0]}"
    assert 0 <= wrapper_target[1] < 240, f"wrapper目标点Y坐标超出范围: {wrapper_target[1]}"
    print(f"✅ wrapper目标点: {wrapper_target}")

def test_sine_wave_math():
    """测试正弦波数学计算的正确性"""
    print("\n=== 测试正弦波数学计算 ===")
    config = SineWaveConfig()
    generator = SineWaveGenerator(config)
    
    # 测试特定点的计算
    # 在X=0处，应该是中心Y位置（相位为0时）
    y_at_0 = generator._calculate_sine_point(0)
    expected_y_at_0 = config.center_y  # sin(0) = 0
    assert abs(y_at_0 - expected_y_at_0) < 1, f"X=0处Y坐标计算错误: 期望{expected_y_at_0}，实际{y_at_0}"
    
    # 在X=80处（1/4周期），应该是最大值
    quarter_x = 320 / (config.frequency * 4)  # 1/4周期的X位置
    y_at_quarter = generator._calculate_sine_point(quarter_x)
    expected_y_at_quarter = config.center_y + config.amplitude  # sin(π/2) = 1
    assert abs(y_at_quarter - expected_y_at_quarter) < 2, f"1/4周期处Y坐标计算错误"
    
    print(f"✅ X=0处Y坐标: {y_at_0} (期望: {expected_y_at_0})")
    print(f"✅ 1/4周期处Y坐标: {y_at_quarter} (期望约: {expected_y_at_quarter})")

def main():
    """主测试函数"""
    print("🚀 开始测试正弦波配置和生成器类")
    
    try:
        test_sine_wave_config()
        test_sine_wave_generator()
        test_wrapper_functions()
        test_sine_wave_math()
        
        print("\n🎉 所有测试通过！正弦波配置和生成器类工作正常。")
        return True
        
    except AssertionError as e:
        print(f"\n❌ 测试失败: {e}")
        return False
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
