#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的配置类定义文件
从三角形识别.py中提取的配置类，用于独立测试
"""

import os
import json


class PathConfig:
    """路径生成配置类"""
    def __init__(self, config_path=None):
        if not config_path:
            # 默认配置文件路径
            config_path = os.path.join(os.path.dirname(__file__), "path_config.json")
        self.config_path = config_path
        
        # 默认配置参数（与当前系统保持一致）
        self.points_per_edge = 50  # 每条边生成的路径点数量
        self.include_centroid = True  # 是否包含重心点作为第一个目标
        self.interpolation_method = 'linear'  # 插值方法：'linear'
        
    def validate(self):
        """验证配置参数的有效性"""
        if not isinstance(self.points_per_edge, int) or self.points_per_edge < 1 or self.points_per_edge > 200:
            raise ValueError(f"points_per_edge必须是1-200之间的整数，当前值: {self.points_per_edge}")
        
        if not isinstance(self.include_centroid, bool):
            raise ValueError(f"include_centroid必须是布尔值，当前值: {self.include_centroid}")
            
        if self.interpolation_method not in ['linear']:
            raise ValueError(f"interpolation_method必须是'linear'，当前值: {self.interpolation_method}")
    
    def save(self):
        """保存配置到JSON文件"""
        try:
            self.validate()  # 保存前验证参数
            config_data = {
                'points_per_edge': self.points_per_edge,
                'include_centroid': self.include_centroid,
                'interpolation_method': self.interpolation_method
            }
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存路径配置失败: {e}")
    
    def load(self):
        """从JSON文件加载配置"""
        if not os.path.exists(self.config_path):
            return  # 文件不存在时使用默认配置
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            # 更新配置参数
            if 'points_per_edge' in config_data:
                self.points_per_edge = config_data['points_per_edge']
            if 'include_centroid' in config_data:
                self.include_centroid = config_data['include_centroid']
            if 'interpolation_method' in config_data:
                self.interpolation_method = config_data['interpolation_method']
                
            self.validate()  # 加载后验证参数
        except Exception as e:
            print(f"加载路径配置失败，使用默认配置: {e}")


class TrackingConfig:
    """激光跟踪配置类"""
    def __init__(self, config_path=None):
        if not config_path:
            # 默认配置文件路径
            config_path = os.path.join(os.path.dirname(__file__), "tracking_config.json")
        self.config_path = config_path
        
        # 默认配置参数（与当前系统保持一致）
        self.target_threshold = 15  # 激光到达目标点的距离阈值（像素）
        self.smoothing_enabled = False  # 是否启用平滑跟踪
        self.prediction_enabled = False  # 是否启用预测跟踪
        
    def validate(self):
        """验证配置参数的有效性"""
        if not isinstance(self.target_threshold, (int, float)) or self.target_threshold < 1 or self.target_threshold > 100:
            raise ValueError(f"target_threshold必须是1-100之间的数值，当前值: {self.target_threshold}")
        
        if not isinstance(self.smoothing_enabled, bool):
            raise ValueError(f"smoothing_enabled必须是布尔值，当前值: {self.smoothing_enabled}")
            
        if not isinstance(self.prediction_enabled, bool):
            raise ValueError(f"prediction_enabled必须是布尔值，当前值: {self.prediction_enabled}")
    
    def save(self):
        """保存配置到JSON文件"""
        try:
            self.validate()  # 保存前验证参数
            config_data = {
                'target_threshold': self.target_threshold,
                'smoothing_enabled': self.smoothing_enabled,
                'prediction_enabled': self.prediction_enabled
            }
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存跟踪配置失败: {e}")
    
    def load(self):
        """从JSON文件加载配置"""
        if not os.path.exists(self.config_path):
            return  # 文件不存在时使用默认配置
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            # 更新配置参数
            if 'target_threshold' in config_data:
                self.target_threshold = config_data['target_threshold']
            if 'smoothing_enabled' in config_data:
                self.smoothing_enabled = config_data['smoothing_enabled']
            if 'prediction_enabled' in config_data:
                self.prediction_enabled = config_data['prediction_enabled']
                
            self.validate()  # 加载后验证参数
        except Exception as e:
            print(f"加载跟踪配置失败，使用默认配置: {e}")


# 测试代码
if __name__ == "__main__":
    print("开始配置类功能测试...")
    
    # 测试PathConfig
    print("\n=== 测试PathConfig类 ===")
    try:
        config = PathConfig()
        print(f"✅ PathConfig初始化成功")
        print(f"   points_per_edge: {config.points_per_edge}")
        print(f"   include_centroid: {config.include_centroid}")
        print(f"   interpolation_method: {config.interpolation_method}")
        
        config.validate()
        print("✅ PathConfig验证通过")
        
        # 测试参数修改
        config.points_per_edge = 100
        config.validate()
        print("✅ PathConfig参数修改验证通过")
        
        # 测试无效参数
        try:
            config.points_per_edge = -1
            config.validate()
            print("❌ 无效参数验证失败")
        except ValueError:
            print("✅ 无效参数验证成功")
            
    except Exception as e:
        print(f"❌ PathConfig测试失败: {e}")
    
    # 测试TrackingConfig
    print("\n=== 测试TrackingConfig类 ===")
    try:
        config = TrackingConfig()
        print(f"✅ TrackingConfig初始化成功")
        print(f"   target_threshold: {config.target_threshold}")
        print(f"   smoothing_enabled: {config.smoothing_enabled}")
        print(f"   prediction_enabled: {config.prediction_enabled}")
        
        config.validate()
        print("✅ TrackingConfig验证通过")
        
        # 测试参数修改
        config.target_threshold = 10.5
        config.validate()
        print("✅ TrackingConfig参数修改验证通过")
        
        # 测试无效参数
        try:
            config.target_threshold = -1
            config.validate()
            print("❌ 无效参数验证失败")
        except ValueError:
            print("✅ 无效参数验证成功")
            
    except Exception as e:
        print(f"❌ TrackingConfig测试失败: {e}")
    
    print("\n✅ 配置类测试完成！")
