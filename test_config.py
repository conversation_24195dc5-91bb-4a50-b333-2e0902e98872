#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置类测试脚本
用于验证PathConfig和TrackingConfig类的功能
"""

import sys
import os
import tempfile
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

# 导入配置类（从三角形识别.py中）
try:
    from 三角形识别 import PathConfig, TrackingConfig
    print("✅ 成功导入配置类")
except ImportError as e:
    print(f"❌ 导入配置类失败: {e}")
    sys.exit(1)

def test_path_config():
    """测试PathConfig类"""
    print("\n=== 测试PathConfig类 ===")
    
    # 创建临时配置文件路径
    temp_dir = tempfile.mkdtemp()
    config_path = os.path.join(temp_dir, "test_path_config.json")
    
    try:
        # 1. 测试默认初始化
        config = PathConfig(config_path)
        print(f"✅ 默认初始化成功")
        print(f"   points_per_edge: {config.points_per_edge}")
        print(f"   include_centroid: {config.include_centroid}")
        print(f"   interpolation_method: {config.interpolation_method}")
        
        # 2. 测试参数验证
        config.validate()
        print("✅ 默认参数验证通过")
        
        # 3. 测试保存功能
        config.save()
        print("✅ 配置保存成功")
        
        # 4. 测试加载功能
        new_config = PathConfig(config_path)
        new_config.load()
        print("✅ 配置加载成功")
        
        # 5. 测试参数修改和验证
        config.points_per_edge = 100
        config.validate()
        print("✅ 参数修改验证通过")
        
        # 6. 测试无效参数验证
        try:
            config.points_per_edge = -1
            config.validate()
            print("❌ 无效参数验证失败")
        except ValueError:
            print("✅ 无效参数验证成功")
            config.points_per_edge = 50  # 恢复有效值
        
        return True
        
    except Exception as e:
        print(f"❌ PathConfig测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(config_path):
            os.remove(config_path)
        os.rmdir(temp_dir)

def test_tracking_config():
    """测试TrackingConfig类"""
    print("\n=== 测试TrackingConfig类 ===")
    
    # 创建临时配置文件路径
    temp_dir = tempfile.mkdtemp()
    config_path = os.path.join(temp_dir, "test_tracking_config.json")
    
    try:
        # 1. 测试默认初始化
        config = TrackingConfig(config_path)
        print(f"✅ 默认初始化成功")
        print(f"   target_threshold: {config.target_threshold}")
        print(f"   smoothing_enabled: {config.smoothing_enabled}")
        print(f"   prediction_enabled: {config.prediction_enabled}")
        
        # 2. 测试参数验证
        config.validate()
        print("✅ 默认参数验证通过")
        
        # 3. 测试保存功能
        config.save()
        print("✅ 配置保存成功")
        
        # 4. 测试加载功能
        new_config = TrackingConfig(config_path)
        new_config.load()
        print("✅ 配置加载成功")
        
        # 5. 测试参数修改和验证
        config.target_threshold = 10.5
        config.validate()
        print("✅ 参数修改验证通过")
        
        # 6. 测试无效参数验证
        try:
            config.target_threshold = -1
            config.validate()
            print("❌ 无效参数验证失败")
        except ValueError:
            print("✅ 无效参数验证成功")
            config.target_threshold = 15  # 恢复有效值
        
        return True
        
    except Exception as e:
        print(f"❌ TrackingConfig测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(config_path):
            os.remove(config_path)
        os.rmdir(temp_dir)

def main():
    """主测试函数"""
    print("开始配置类功能测试...")
    
    path_test_result = test_path_config()
    tracking_test_result = test_tracking_config()
    
    print("\n=== 测试结果汇总 ===")
    if path_test_result and tracking_test_result:
        print("✅ 所有测试通过！配置类实现正确。")
        return True
    else:
        print("❌ 部分测试失败，请检查配置类实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
